using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Adapter;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using MediatR;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Application.Queries;
using Vat.Service.Application.Queries.Crm;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications;
using Vat.Service.Domain.Common.Models;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class AddServiceAgreementCommandHandler : IRequestHandler<AddServiceAgreementCommand, string>
    {
        private readonly IDBContext _dbContext;
        private readonly IServiceAgreementRepository _serviceAgreementRepository;
        private readonly ITypeAdapter _typeAdapter;
        private readonly ILogger<AddServiceAgreementCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;
        private readonly ICrmQueries _crmQueries;

        public AddServiceAgreementCommandHandler(IDBContext dbContext, IServiceAgreementRepository serviceAgreementRepository, ITypeAdapter typeAdapter, ILogger<AddServiceAgreementCommandHandler> logger, IAdminIdentityService adminIdentityService, ICrmQueries crmQueries)
        {
            _dbContext = dbContext;
            _serviceAgreementRepository = serviceAgreementRepository;
            _typeAdapter = typeAdapter;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
            _crmQueries = crmQueries;
        }

        public async Task<string> Handle(AddServiceAgreementCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            string code = request.AgreementCode;
            bool exists = await _serviceAgreementRepository.ExistsAsync(new MatchServiceAgreementByIsDeleteSpecification(false)
                .And(new MatchServiceAgreementByAgreementCodeSpecification(code)));
            if (exists || code.IsNullOrBlank())
            {
                code = await _crmQueries.GetLastestServiceAgreementCode();
            }
            List<ServiceProject> projects = null;
            if (request.Projects != null)
            {
                projects = request.Projects.Select(item => item.ToDomain()).ToList();
            }
            ServiceAgreement agreement = new ServiceAgreement(request.Status, request.SignDate, request.CustomerId, code, request.ChannelCode, request.Country, request.ProjectCode, request.RegisterType, request.ContractStartDate, request.ContractEndDate, request.ContractAmount.ToDomain(), request.ReceiveAmount.ToDomain(), request.Salesman, request.CustomerServer, request.TaxManager, request.PartnerCode, request.CommissionAmount.ToDomain(), request.PaymentAmount.ToDomain(), request.Agent, request.CrmAgent, request.DeclareMethod, request.Remark, request.AggrementFile, request.AggrementFile2, request.AggrementFile3, user.FullName, request.DeclareStatus, request.AgentAdress, request.Collaborators, request.BackendAccountant, request.InvoicedAmount.ToDomain(), request.UnInvoicedAmount.ToDomain(), request.PaidAmount.ToDomain(), request.UnPaidAmount.ToDomain(), request.MarketSource, request.Correlation, projects);
            _dbContext.BeginTransaction();
            await _serviceAgreementRepository.AddAsync(agreement);
            await _dbContext.CommitAsync();
            return agreement.Id;
        }
    }
}
