using BFE.Framework.Domain.Core;
using BFE.Framework.Infrastructure.Authorization.Admin;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Vat.Service.Application.Extensions;
using Vat.Service.Domain.AggregatesModel.CrmAgg;
using Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications;
using Vat.Service.Domain.Common.Models;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Application.Commands.Crm.Handlers
{
    public class AddServiceAgreementCommandHandler : IRequestHandler<AddServiceAgreementCommand, string>
    {
        private readonly IDBContext _dbContext;
        private readonly IServiceAgreementRepository _serviceAgreementRepository;
        private readonly IServiceAgreementCodeSequenceRepository _codeSequenceRepository;
        private readonly ILogger<AddServiceAgreementCommandHandler> _logger;
        private readonly IAdminIdentityService _adminIdentityService;

        public AddServiceAgreementCommandHandler(IDBContext dbContext, IServiceAgreementRepository serviceAgreementRepository, IServiceAgreementCodeSequenceRepository codeSequenceRepository, ILogger<AddServiceAgreementCommandHandler> logger, IAdminIdentityService adminIdentityService)
        {
            _dbContext = dbContext;
            _serviceAgreementRepository = serviceAgreementRepository;
            _codeSequenceRepository = codeSequenceRepository;
            _logger = logger;
            _adminIdentityService = adminIdentityService;
        }

        public async Task<string> Handle(AddServiceAgreementCommand request, CancellationToken cancellationToken)
        {
            var user = _adminIdentityService.GetAdminUserIdentity();
            string code = request.AgreementCode;
            bool exists = await _serviceAgreementRepository.ExistsAsync(new MatchServiceAgreementByIsDeleteSpecification(false)
                .And(new MatchServiceAgreementByAgreementCodeSpecification(code)));
            if (exists || code.IsNullOrBlank())
            {
                code = await GenerateUniqueAgreementCodeAsync();
            }
            List<ServiceProject> projects = null;
            if (request.Projects != null)
            {
                projects = request.Projects.Select(item => item.ToDomain()).ToList();
            }
            ServiceAgreement agreement = new ServiceAgreement(request.Status, request.SignDate, request.CustomerId, code, request.ChannelCode, request.Country, request.ProjectCode, request.RegisterType, request.ContractStartDate, request.ContractEndDate, request.ContractAmount.ToDomain(), request.ReceiveAmount.ToDomain(), request.Salesman, request.CustomerServer, request.TaxManager, request.PartnerCode, request.CommissionAmount.ToDomain(), request.PaymentAmount.ToDomain(), request.Agent, request.CrmAgent, request.DeclareMethod, request.Remark, request.AggrementFile, request.AggrementFile2, request.AggrementFile3, user.FullName, request.DeclareStatus, request.AgentAdress, request.Collaborators, request.BackendAccountant, request.InvoicedAmount.ToDomain(), request.UnInvoicedAmount.ToDomain(), request.PaidAmount.ToDomain(), request.UnPaidAmount.ToDomain(), request.MarketSource, request.Correlation, projects);
            _dbContext.BeginTransaction();
            await _serviceAgreementRepository.AddAsync(agreement);
            await _dbContext.CommitAsync();
            return agreement.Id;
        }

        /// <summary>
        /// 生成唯一的协议编码（原子操作）
        /// </summary>
        /// <returns></returns>
        private async Task<string> GenerateUniqueAgreementCodeAsync()
        {
            const int maxRetries = 10;
            var today = DateTime.Now.Date;
            var dateKey = today.ToString("yyyyMMdd");
            var datePrefix = $"XY{dateKey}";

            for (int retry = 0; retry < maxRetries; retry++)
            {
                try
                {
                    _dbContext.BeginTransaction();

                    // 查找或创建当天的序列记录
                    var sequence = await _codeSequenceRepository.GetAsync(
                        new MatchServiceAgreementCodeSequenceByDateKeySpecification(dateKey));

                    if (sequence == null)
                    {
                        // 如果不存在，创建新的序列记录
                        sequence = new ServiceAgreementCodeSequence(dateKey, 0);
                        await _codeSequenceRepository.AddAsync(sequence);
                    }

                    // 获取下一个序号
                    var nextSequence = sequence.GetNextSequence();
                    var candidateCode = $"{datePrefix}{nextSequence.ToString().PadLeft(4, '0')}";

                    // 更新序列记录
                    await _codeSequenceRepository.UpdateAsync(sequence);

                    // 双重检查：确保生成的编码不存在（防止数据不一致）
                    var codeExists = await _serviceAgreementRepository.ExistsAsync(
                        new MatchServiceAgreementByAgreementCodeSpecification(candidateCode));

                    if (!codeExists)
                    {
                        await _dbContext.CommitAsync();
                        return candidateCode;
                    }

                    // 如果编码已存在，说明数据不一致，继续重试
                    await _dbContext.CommitAsync();
                    _logger.LogWarning($"生成的协议编码 {candidateCode} 已存在，重试第 {retry + 1} 次");
                    await Task.Delay(50 + retry * 10);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"生成协议编码失败，重试第 {retry + 1} 次: {ex.Message}");

                    try
                    {
                        await _dbContext.CommitAsync(); // 尝试提交以清理事务状态
                    }
                    catch
                    {
                        // 忽略提交异常
                    }

                    await Task.Delay(100 + retry * 20);
                }
            }

            // 如果所有重试都失败，使用带毫秒时间戳的编码作为后备方案
            var timestamp = DateTime.Now.ToString("HHmmss") + DateTime.Now.Millisecond.ToString("000");
            var fallbackCode = $"{datePrefix}{timestamp}";

            _logger.LogError($"协议编码生成重试次数已达上限，使用后备编码: {fallbackCode}");
            return fallbackCode;
        }
    }
}
