﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Vat.Service.Application.ViewModels.Crm.Request
{
    public class ChannelParam
    {

        public ChannelParam()
        {
            this.PageNumber = 1;
            this.PageSize = 20;
        }
        /// <summary>
        /// 关键字
        /// </summary>
        public string KeyWord { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 角色名称
        /// </summary>
        public string Role { get; set; }
        /// <summary>
        /// 联系方式
        /// </summary>
        public string ContactType { get; set; }
        /// <summary>
        /// 渠道编码
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; set; }
        /// <summary>
        /// 联系方式-手机号字段
        /// </summary>
        public string Telephone { get; set; }
        /// <summary>
        /// 联系方式-邮箱字段
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 佣金比例
        /// </summary>
        public decimal? CommissionRate { get; set; }
        /// <summary>
        /// 销售人员
        /// </summary>
        public string Salesman { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateUser { get; set; }
        /// <summary>
        /// 编辑人
        /// </summary>
        public string ModifyUser { get; set; }

        public int PageNumber { get; set; }


        public int PageSize { get; set; }
        public List<string> CheckIds { get; set; }
    }
}
