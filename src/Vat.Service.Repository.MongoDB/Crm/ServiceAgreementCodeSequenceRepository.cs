using BFE.Framework.Infrastructure.Data.MongoDB;
using BFE.Framework.Infrastructure.Data.MongoDB.Impl;
using Vat.Service.Domain.Repositories;

namespace Vat.Service.Repository.MongoDB.CrmCustomer
{
    public class ServiceAgreementCodeSequenceRepository : MongoDBRepository<Domain.AggregatesModel.CrmAgg.ServiceAgreementCodeSequence>, IServiceAgreementCodeSequenceRepository
    {
        public ServiceAgreementCodeSequenceRepository(IMongoDBContext context) : base(context)
        {
        }
    }
}
