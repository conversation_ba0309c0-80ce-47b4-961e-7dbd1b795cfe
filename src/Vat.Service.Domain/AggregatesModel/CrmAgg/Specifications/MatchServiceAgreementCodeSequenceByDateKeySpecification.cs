using BFE.Framework.Domain.Core.Specification;
using System;
using System.Linq.Expressions;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg.Specifications
{
    public class MatchServiceAgreementCodeSequenceByDateKeySpecification : Specification<ServiceAgreementCodeSequence>
    {
        private readonly string _dateKey;

        public MatchServiceAgreementCodeSequenceByDateKeySpecification(string dateKey)
        {
            _dateKey = dateKey;
        }

        public override Expression<Func<ServiceAgreementCodeSequence, bool>> GetExpression()
        {
            return p => p.DateKey == _dateKey;
        }
    }
}
