using BFE.Framework.Domain.Core.Attributes;
using BFE.Framework.Domain.Core.Impl;
using System;

namespace Vat.Service.Domain.AggregatesModel.CrmAgg
{
    /// <summary>
    /// 服务协议编码序列
    /// </summary>
    [AggregateRootName("ServiceAgreementCodeSequences")]
    public class ServiceAgreementCodeSequence : AggregateRoot
    {
        public ServiceAgreementCodeSequence(string dateKey, int currentSequence)
        {
            DateKey = dateKey;
            CurrentSequence = currentSequence;
            CreateTime = DateTime.Now;
            UpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 日期键（格式：yyyyMMdd）
        /// </summary>
        public string DateKey { get; private set; }

        /// <summary>
        /// 当前序号
        /// </summary>
        public int CurrentSequence { get; private set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; private set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; private set; }

        /// <summary>
        /// 获取下一个序号
        /// </summary>
        /// <returns></returns>
        public int GetNextSequence()
        {
            CurrentSequence++;
            UpdateTime = DateTime.Now;
            return CurrentSequence;
        }
    }
}
