﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BFE.Framework.Infrastructure.Crosscutting.Constant;
using BFE.Framework.Infrastructure.Crosscutting.Helpers;
using Microsoft.Extensions.Logging;
using Vat.Service.Common.Model;
using Vat.Service.Common.Model.SaleAmountStatistics;
using Vat.Service.Domain.AggregatesModel.NationalBusinessAgg.Specifications;
using Vat.Service.Domain.AggregatesModel.VATExchangeRateAgg;
using Vat.Service.Domain.Repositories.NationalBusiness;

namespace Vat.Service.Common.Services.Impl.DeclaredCalculateFormula
{
    public class GBDeclaredCalculateFormula : IDeclaredCalculateFormula
    {
        private readonly ILogger<GBDeclaredCalculateFormula> _logger;
        private readonly string _countryCode = "GB";
        private readonly string _currencyCode = "GBP";
        private readonly decimal _taxrate = 0.2M;

        public GBDeclaredCalculateFormula(ILogger<GBDeclaredCalculateFormula> logger)
        {
            this._logger = logger;
        }
        //private readonly INationalBusinessReadOnlyRepository _nationalBusinessReadOnlyRepository;

        //public GBDeclaredCalculateFormula(INationalBusinessReadOnlyRepository nationalBusinessReadOnlyRepository)
        //{
        //    _nationalBusinessReadOnlyRepository = nationalBusinessReadOnlyRepository;
        //}


        /// <summary>
        /// 亚马逊计算-2025年之前的计算方式
        /// </summary>
        /// <param name="customerSalesDatas"></param>
        /// <param name="declareMonthList"></param>
        /// <param name="euCountryList"></param>
        /// <param name="declareMonthDic"></param>
        /// <param name="arrivalCountryList"></param>
        /// <param name="exchangeRateList"></param>
        /// <param name="euCountryDic"></param>
        /// <param name="existVatCountryCodeList"></param>
        /// <returns></returns>
        public async Task<SaleAmountStatisticsResult> AmazonSalesAmountCalculate(List<CustomerSalesData> customerSalesDatas, List<string> declareMonthList, List<string> euCountryList,
           Dictionary<string, string> declareMonthDic, List<string> arrivalCountryList, List<VatExchangeRate> exchangeRateList, Dictionary<string, string> euCountryDic,
            List<string> existVatCountryCodeList)
        {
            var result = new SaleAmountStatisticsResult();
            var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
            var currencyList = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).Select(c => c.Value).ToList();

            var salesDatas = customerSalesDatas.Where(c => (declareMonthList.Contains(c.ACTIVITY_PERIOD)) && c.TAX_COLLECTION_RESPONSIBILITY != null).ToList();

            var euAmazonItems = GetDeclaredDetail(declareMonthList, declareMonthDic, salesDatas, exchangeRateList, currencyList, euCountryList);
            var euStatistics = new EUAmazonSaleAmountStatistics()
            {
                // 未被代扣净销售额
                AmazonSaleAmount = new Money(euAmazonItems.Sum(i => i.EUToNative.Amount), _currencyCode),
                EUAmazonItems = euAmazonItems,
                // 被代扣代缴净销售额
                ExportAmount = new Money(euAmazonItems.Sum(i => i.Export.Amount), _currencyCode),
                ZMDeclaredAmount = new Money(0, _currencyCode)
            };
            result.EUAmazonSaleAmountStatistics = euStatistics;
            return result;
        }
        public List<EUAmazonItem> GetDeclaredDetail(List<string> declareMonthList, Dictionary<string, string> declareMonthDic, List<CustomerSalesData> salesData, List<VatExchangeRate> exchangeRateList,
          List<string> exchangeCurrenyCodes, List<string> euCountryList)
        {
            List<EUAmazonItem> result = new List<EUAmazonItem>();
            var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
            if (!currencyDic.ContainsKey("GBP"))
                currencyDic.Add("GBP", "英镑");
            if (!exchangeCurrenyCodes.Contains("GBP"))
                exchangeCurrenyCodes.Add("GBP");
            foreach (var item in declareMonthList)
            {
                var month = declareMonthDic[item];
                var sellerData = salesData.Where(c => c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller" && c.ACTIVITY_PERIOD == item && c.SALE_DEPART_COUNTRY == _countryCode && c.SALE_ARRIVAL_COUNTRY == _countryCode).ToList();

                var marketData = salesData.Where(c => c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "marketplace" && c.ACTIVITY_PERIOD == item
                && c.SALE_DEPART_COUNTRY == _countryCode && c.SALE_ARRIVAL_COUNTRY == _countryCode).ToList();
                List<EUCurrencyItem> euCurItems = new List<EUCurrencyItem>();
                foreach (var exchangeCurrencyCode in exchangeCurrenyCodes)
                {
                    decimal exchangeRate = 1M;
                    if (exchangeCurrencyCode != _currencyCode)
                    {
                        exchangeRate = exchangeRateList.Where(c => c.VatCountryCurrency == currencyDic[_currencyCode] && c.ExchangeCurrency == currencyDic[exchangeCurrencyCode] &&
                   c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();
                    }
                    //第 1 步：本 国 B 2 B :
                    var b2bdata = sellerData.Where(c => !c.BUYER_VAT_NUMBER.IsNullOrBlank() && c.TRANSACTION_CURRENCY_CODE.Equals(exchangeCurrencyCode) && !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank());
                    var b2b = b2bdata.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));
                    //未 代 扣 代 缴 B 2 C:
                    var b2cdata = sellerData.Where(c => c.BUYER_VAT_NUMBER.IsNullOrBlank() && (c.ARRIVAL_POST_CODE.IsNullOrBlank() || (!c.ARRIVAL_POST_CODE.IsNullOrBlank() && !c.ARRIVAL_POST_CODE.StartsWith("JE") && !c.ARRIVAL_POST_CODE.StartsWith("GY"))) && c.TRANSACTION_CURRENCY_CODE.Equals(exchangeCurrencyCode) && !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank());
                    var b2c = b2cdata.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                    //2.被平台代扣代缴净销售额（平台已经代扣代缴，只申报不交税）
                    var transation_currency_code_datas = marketData.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals(exchangeCurrencyCode) && !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank()).ToList();
                    var totalAmount = transation_currency_code_datas.Sum(d => decimal.Parse(d.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));


                    EUCurrencyItem euCurItem = new EUCurrencyItem
                    {
                        CurrencySalesAmount = new Money(totalAmount, exchangeCurrencyCode),
                        ExchangeRate = Math.Round(1 / exchangeRate, 4),
                        OrderSalesAmount = new Money(b2c, exchangeCurrencyCode),
                        NativeB2BSalesAmount = new Money(b2b, exchangeCurrencyCode)
                    };
                    euCurItems.Add(euCurItem);
                }
                EUAmazonItem euitem = new EUAmazonItem()
                {
                    Month = month,
                    EUToNative = new Money(Math.Round(euCurItems.Sum(i => i.ExchangeRate * (i.OrderSalesAmount.Amount + i.NativeB2BSalesAmount.Amount)), 2), _currencyCode),
                    EUCurrencyItems = euCurItems,
                    Export = new Money(Math.Round(euCurItems.Sum(i => i.ExchangeRate * i.CurrencySalesAmount.Amount), 2), _currencyCode)
                };

                result.Add(euitem);
            }
            return result;
        }

        public async Task<SaleAmountStatisticsResult> AmazonSalesAmountCalculate3(List<CustomerSalesData> customerSalesDatas, List<string> declareMonthList, List<string> euCountryList,
           Dictionary<string, string> declareMonthDic, List<string> arrivalCountryList, List<VatExchangeRate> exchangeRateList, Dictionary<string, string> euCountryDic,
            List<string> existVatCountryCodeList)
        {
            //var nationalBusiness = await _nationalBusinessReadOnlyRepository.GetAsync(new MatchNationalBusinessByCountryCodeSpecification("GB"));
            var result = new SaleAmountStatisticsResult();
            DateTime pointTime = Convert.ToDateTime("2021-01-01");
            List<string> newdeclareMonthList = declareMonthList.Where(t => Convert.ToDateTime(t) >= pointTime).ToList();
            List<string> beforedeclareMonthList = declareMonthList.Where(t => Convert.ToDateTime(t) < pointTime).ToList();
            var transactionTypes = new List<string>() { "REFUND", "SALE", "LIQUIDATION_SALE" };
            customerSalesDatas.ForEach(item =>
            {
                if (item.SALE_ARRIVAL_COUNTRY == "MC")
                    item.SALE_ARRIVAL_COUNTRY = "FR";
            });
            //第二步：在TRANSACTION_TYPE 列，筛选保留单元格为REFUND 、SALE 这两个字段的数据。
            var salesDatas = customerSalesDatas.Where(c => transactionTypes.Contains(c.TRANSACTION_TYPE)
            && (beforedeclareMonthList.Contains(c.ACTIVITY_PERIOD))).ToList();
            //var salesDatas = customerSalesDatas.Where(c => declareMonthList.Contains(c.ACTIVITY_PERIOD)).ToList();

            //第三步：在SALE_DEPART_COUNTRY 列，筛选保留单元格为 VatCountryCode 这个字段的数据。(英国：GB)
            var sale_Depart_Country_Datas = salesDatas.Where(c => c.SALE_DEPART_COUNTRY.Equals(_countryCode)).ToList();

            //第四步：在SALE_ARRIVAL_COUNTRY 列，剔除数据对象：【有VAT账号的欧盟国家数据 + 欧盟以外的国家数据】，
            //即保留对象：【没有VAT账号的欧盟国家数据 + 申报国数据】。
            if (!arrivalCountryList.Contains(_countryCode))
                arrivalCountryList.Add(_countryCode);
            sale_Depart_Country_Datas = sale_Depart_Country_Datas
                .Where(c => arrivalCountryList.Contains(c.SALE_ARRIVAL_COUNTRY)).ToList();

            //第五步：在TRANSACTION_CURRENCY_CODE 列，筛选保留单元格为 申报国币种（例如应该:GBP） 字段的数据。
            var currenyCode = "GBP";
            var transation_Currency_Code_CurrenyCode_Datas = sale_Depart_Country_Datas
                .Where(c => c.TRANSACTION_CURRENCY_CODE.Equals(currenyCode)).ToList();

            //第六步：将TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL 列的数据，求和计算。
            var totalGBPActivityValue = transation_Currency_Code_CurrenyCode_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));


            #region 2021年之后的计算规则
            var newsalesDatas = customerSalesDatas.Where(c => newdeclareMonthList.Contains(c.ACTIVITY_PERIOD)).ToList();

            //第三步：在SALE_DEPART_COUNTRY 列，筛选保留单元格为 VatCountryCode 这个字段的数据。(英国：GB)
            var newsale_Depart_Country_Datas = newsalesDatas.Where(c => c.MARKETPLACE == "amazon.co.uk" && c.TRANSACTION_CURRENCY_CODE == currenyCode && transactionTypes.Contains(c.TRANSACTION_TYPE)).ToList();
            var newCalcTotalAmount = newsale_Depart_Country_Datas.Where(c => !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL.IsNullOrBlank()).Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL));
            var newCalcTotalAmtAmount = newsale_Depart_Country_Datas.Where(c => !c.TOTAL_ACTIVITY_VALUE_VAT_AMT.IsNullOrBlank()).Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_VAT_AMT));
            #endregion

            //第十二步：根据第二步筛选保留的REFUND 、SALE 这两个字段的数据，做第二次筛选。
            //在SALE_ARRIVAL_COUNTRY列，筛选保留单元格为 (英国GB 德国DE) 这个字段的数据。
            var second_sale_Depart_Country_Datas = salesDatas.Where(c => c.SALE_ARRIVAL_COUNTRY.Equals(_countryCode)).ToList();

            //第十三步：在SALE_DEPART_COUNTRY 列，筛选保留除了英国以外的所有欧盟国家的数据。（保留字段为：IT、DE、SE等等，除去英国GB 德国DE）
            var sale_Depart_Country_exceptGB_Datas = second_sale_Depart_Country_Datas.Where(c => !c.SALE_DEPART_COUNTRY.Equals(_countryCode)
            && euCountryList.Contains(c.SALE_DEPART_COUNTRY)).ToList();

            //第十四步：在TRANSACTION_CURRENCY_CODE 列，筛选保留单元格为 GBP 这个字段的数据。
            var transaction_currency_code_GBP_Datas = sale_Depart_Country_exceptGB_Datas.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals("GBP")).ToList();
            var transaction_currency_code_EUR_Datas = sale_Depart_Country_exceptGB_Datas.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals("EUR")).ToList();

            decimal totalexceptVatCountryCodeActivityValue = 0;
            var tempValue = transaction_currency_code_GBP_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

            var exchangeRateMothList = declareMonthDic.Values.ToList();

            decimal totalEURActivityValue = 0;

            var saleAmountStatisticsItemDetailByMonths = new List<AmazonSaleAmountStatisticsItemDetailByMonth>();
            foreach (var item in beforedeclareMonthList)
            {
                #region part1
                var month = declareMonthDic[item];
                var exchangeRate = exchangeRateList.Where(c => c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();
                if (exchangeRate == 0)
                {
                    continue;
                }
                var transation_Currency_Code_EUR_Datas = sale_Depart_Country_Datas.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals("EUR")
                && c.ACTIVITY_PERIOD.Equals(item)).ToList();
                var value = transation_Currency_Code_EUR_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));
                if (value != 0)
                {
                    totalEURActivityValue += value / exchangeRate;
                }
                var localValue = transation_Currency_Code_CurrenyCode_Datas.Where(c => c.ACTIVITY_PERIOD.Equals(item))
                    .Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));
                #endregion
                #region part2
                var transation_EUR_Datas = transaction_currency_code_EUR_Datas.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals("EUR")
                   && c.ACTIVITY_PERIOD.Equals(item)).ToList();
                var valueTransEur = transation_EUR_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));
                if (valueTransEur != 0)
                {
                    totalexceptVatCountryCodeActivityValue += valueTransEur / exchangeRate;
                }
                var localValueTransEur = transaction_currency_code_GBP_Datas.Where(c => c.ACTIVITY_PERIOD.Equals(item))
                .Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));
                #endregion
                var localSaleamount = localValue + localValueTransEur;
                var eurSalesamount = value + valueTransEur;
                var saleAmountStatisticsItemDetail = new SaleAmountStatisticsItemDetail();
                saleAmountStatisticsItemDetail.StatisticalPeriod = month;
                saleAmountStatisticsItemDetail.ExchangeRate = 1;
                saleAmountStatisticsItemDetail.GBPToEuroExchangeRate = exchangeRate;
                saleAmountStatisticsItemDetail.EurStatisticalTaxAmount = new Money()
                {
                    Amount = eurSalesamount == 0 ? 0 : Math.Round((eurSalesamount / (1 + _taxrate)) * _taxrate, 2),
                    CurrencyCode = "EUR"
                };
                saleAmountStatisticsItemDetail.StatisticalTaxAmount = new Money()
                {
                    Amount = localSaleamount == 0 ? 0 : Math.Round((localSaleamount / (1 + _taxrate)) * _taxrate, 2),
                    CurrencyCode = "GBP"
                };
                saleAmountStatisticsItemDetail.StatisticalAmount = new Money()
                {
                    Amount = eurSalesamount == 0 ? 0 : Math.Round(eurSalesamount, 2),
                    CurrencyCode = "EUR"
                };
                saleAmountStatisticsItemDetail.LocalCurrencyStatisticalAmount = new Money()
                {
                    Amount = localSaleamount == 0 ? 0 : Math.Round(localSaleamount, 2),
                    CurrencyCode = "GBP"
                };
                saleAmountStatisticsItemDetailByMonths.Add(new AmazonSaleAmountStatisticsItemDetailByMonth() { DeclareMonth = month, Detail = saleAmountStatisticsItemDetail });

                //var saleAmountStatisticsItemDetailByMonth = saleAmountStatisticsItemDetailByMonths.FirstOrDefault(p => p.DeclareMonth == month);
                //if (transaction_currency_code_EUR_Datas.Count() > 0)
                //{
                //var transation_EUR_Datas = transaction_currency_code_EUR_Datas.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals("EUR")
                //&& c.ACTIVITY_PERIOD.Equals(item)).ToList();
                //var valueTransEur = transation_EUR_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));
                //if (valueTransEur != 0)
                //{
                //    totalexceptVatCountryCodeActivityValue += valueTransEur / exchangeRate;
                //}
                //var localValueTransEur = transaction_currency_code_GBP_Datas.Where(c => c.ACTIVITY_PERIOD.Equals(item))
                //.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                //saleAmountStatisticsItemDetailByMonth.Detail.StatisticalAmount.Amount += valueTransEur;
                //saleAmountStatisticsItemDetailByMonth.Detail.LocalCurrencyStatisticalAmount.Amount += localValueTransEur;
                //}
                //else
                //{
                //var localValueTransEur = transaction_currency_code_GBP_Datas.Where(c => c.ACTIVITY_PERIOD.Equals(item))
                //.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));
                //saleAmountStatisticsItemDetailByMonth.Detail.LocalCurrencyStatisticalAmount.Amount += localValueTransEur;
                //}

            }
            foreach (var item in newdeclareMonthList) //这里是2021年以后
            {
                var month = declareMonthDic[item];
                var exchangeRate = exchangeRateList.Where(c => c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();
                var itemList = newsale_Depart_Country_Datas.Where(c => c.ACTIVITY_PERIOD.Equals(item));
                var localValue = itemList.Where(c => !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL.IsNullOrBlank()).Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL));


                var saleAmountStatisticsItemDetail = new SaleAmountStatisticsItemDetail();
                saleAmountStatisticsItemDetail.StatisticalPeriod = month;
                saleAmountStatisticsItemDetail.ExchangeRate = 1;
                saleAmountStatisticsItemDetail.GBPToEuroExchangeRate = exchangeRate;
                saleAmountStatisticsItemDetail.EurStatisticalTaxAmount = new Money()
                {
                    Amount = 0,
                    CurrencyCode = "EUR"
                };
                saleAmountStatisticsItemDetail.StatisticalTaxAmount = new Money()
                {
                    Amount = localValue == 0 ? 0 : Math.Round(itemList.Where(c => !c.TOTAL_ACTIVITY_VALUE_VAT_AMT.IsNullOrBlank()).Sum(p => decimal.Parse(p.TOTAL_ACTIVITY_VALUE_VAT_AMT)), 2),
                    CurrencyCode = "GBP"
                };
                saleAmountStatisticsItemDetail.StatisticalAmount = new Money()
                {
                    Amount = 0,
                    CurrencyCode = "EUR"
                };
                saleAmountStatisticsItemDetail.LocalCurrencyStatisticalAmount = new Money()
                {
                    Amount = localValue == 0 ? 0 : Math.Round(localValue, 2),
                    CurrencyCode = "GBP"
                };
                saleAmountStatisticsItemDetailByMonths.Add(new AmazonSaleAmountStatisticsItemDetailByMonth() { DeclareMonth = month, Detail = saleAmountStatisticsItemDetail });
            }
            result.GBAmazonSaleAmountStatistics.SaleAmountStatisticsItemDetailByMonths = saleAmountStatisticsItemDetailByMonths;
            var saleAmount = totalGBPActivityValue + tempValue + totalEURActivityValue + totalexceptVatCountryCodeActivityValue;//这个是去年的
            saleAmount = Math.Round(saleAmount, 2);
            result.GBAmazonSaleAmountStatistics.NormalSaleAmount = new Money()
            {
                Amount = Math.Round(saleAmount + newCalcTotalAmount, 2),
                CurrencyCode = "GBP"
            };
            result.GBAmazonSaleAmountStatistics.NormalSaleAmountAmtTax = new Money()
            {
                Amount = Math.Round(newCalcTotalAmtAmount + (saleAmount / (1 + _taxrate) * _taxrate), 2),
                CurrencyCode = "GBP"
            };

            return result;
        }
        #region 老版本备份
        /// <summary>
        /// amazon销售计算
        /// </summary>
        /// <param name="customerSalesDatas"></param>
        /// <param name="declareMonthList"></param>
        /// <param name="euCountryList"></param>
        /// <param name="declareMonthDic"></param>
        /// <param name="arrivalCountryList"></param>
        /// <returns></returns>
        public async Task<SaleAmountStatisticsResult> AmazonSalesAmountCalculate2(List<CustomerSalesData> customerSalesDatas, List<string> declareMonthList, List<string> euCountryList,
           Dictionary<string, string> declareMonthDic, List<string> arrivalCountryList, List<VatExchangeRate> exchangeRateList, Dictionary<string, string> euCountryDic,
            List<string> existVatCountryCodeList)
        {
            var result = new SaleAmountStatisticsResult();
            DateTime pointTime = Convert.ToDateTime("2021-01-01");
            List<string> newdeclareMonthList = declareMonthList.Where(t => Convert.ToDateTime(t) >= pointTime).ToList();
            declareMonthList = declareMonthList.Where(t => Convert.ToDateTime(t) < pointTime).ToList();
            //var transactionTypes = new List<string>() { "REFUND", "SALE" };
            customerSalesDatas.ForEach(item =>
            {
                if (item.SALE_ARRIVAL_COUNTRY == "MC")
                    item.SALE_ARRIVAL_COUNTRY = "FR";
            });
            //第二步：在TRANSACTION_TYPE 列，筛选保留单元格为REFUND 、SALE 这两个字段的数据。
            //var salesDatas = customerSalesDatas.Where(c => transactionTypes.Contains(c.TRANSACTION_TYPE)
            //&& (declareMonthList.Contains(c.ACTIVITY_PERIOD))).ToList();
            var salesDatas = customerSalesDatas.Where(c => declareMonthList.Contains(c.ACTIVITY_PERIOD)).ToList();

            //第三步：在SALE_DEPART_COUNTRY 列，筛选保留单元格为 VatCountryCode 这个字段的数据。(英国：GB)
            var sale_Depart_Country_Datas = salesDatas.Where(c => c.SALE_DEPART_COUNTRY.Equals(_countryCode)).ToList();

            //第四步：在SALE_ARRIVAL_COUNTRY 列，剔除数据对象：【有VAT账号的欧盟国家数据 + 欧盟以外的国家数据】，
            //即保留对象：【没有VAT账号的欧盟国家数据 + 申报国数据】。
            if (!arrivalCountryList.Contains(_countryCode))
                arrivalCountryList.Add(_countryCode);
            sale_Depart_Country_Datas = sale_Depart_Country_Datas
                .Where(c => arrivalCountryList.Contains(c.SALE_ARRIVAL_COUNTRY)).ToList();

            //第五步：在TRANSACTION_CURRENCY_CODE 列，筛选保留单元格为 申报国币种（例如应该:GBP） 字段的数据。
            var currenyCode = "GBP";
            var transation_Currency_Code_CurrenyCode_Datas = sale_Depart_Country_Datas
                .Where(c => c.TRANSACTION_CURRENCY_CODE.Equals(currenyCode)).ToList();

            //第六步：将TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL 列的数据，求和计算。
            var totalGBPActivityValue = transation_Currency_Code_CurrenyCode_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

            //返回结果 英国发往欧盟Item赋值
            var computationalName = $@"英国发往欧盟的销售金额";
            var normalSaleAmountStatisticsListItem = new SaleAmountStatisticsItem();
            normalSaleAmountStatisticsListItem.ComputationalName = computationalName;
            normalSaleAmountStatisticsListItem.VatDeclareCountryAmount = new Money
            {
                Amount = Math.Round(totalGBPActivityValue, 4),
                CurrencyCode = "GBP"
            };
            #region 2021年之后的计算规则
            var newsalesDatas = customerSalesDatas.Where(c => newdeclareMonthList.Contains(c.ACTIVITY_PERIOD)).ToList();

            //第三步：在SALE_DEPART_COUNTRY 列，筛选保留单元格为 VatCountryCode 这个字段的数据。(英国：GB)
            var newsale_Depart_Country_Datas = newsalesDatas.Where(c => c.MARKETPLACE == "amazon.co.uk" && c.TRANSACTION_CURRENCY_CODE.Equals(currenyCode)).ToList();
            var newCalcTotalAmount = newsale_Depart_Country_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL));
            var newCalcTotalAmtAmount = newsale_Depart_Country_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_VAT_AMT));
            #endregion
            var exchangeRateMothList = declareMonthDic.Values.ToList();

            decimal totalEURActivityValue = 0;

            //第七步 第八步 第九步 第十步 第十一步：在TRANSACTION_CURRENCY_CODE 列，筛选保留单元格为EUR这个字段的数据（EUR有可能有，也有可能没有）。
            foreach (var item in declareMonthList)//这里是2021年以前的
            {
                var month = declareMonthDic[item];
                var exchangeRate = exchangeRateList.Where(c => c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();
                if (exchangeRate == 0)
                {
                    continue;
                }
                var transation_Currency_Code_EUR_Datas = sale_Depart_Country_Datas.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals("EUR")
                && c.ACTIVITY_PERIOD.Equals(item)).ToList();
                var value = transation_Currency_Code_EUR_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));
                if (value != 0)
                {
                    totalEURActivityValue += value / exchangeRate;
                }
                var localValue = transation_Currency_Code_CurrenyCode_Datas.Where(c => c.ACTIVITY_PERIOD.Equals(item))
                    .Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));



                var normalAmazonSaleAmountStatisticsItemDetail = new SaleAmountStatisticsItemDetail();
                normalAmazonSaleAmountStatisticsItemDetail.StatisticalPeriod = month;
                normalAmazonSaleAmountStatisticsItemDetail.ExchangeRate = exchangeRate;
                normalAmazonSaleAmountStatisticsItemDetail.StatisticalAmount = new Money()
                {
                    Amount = value == 0 ? 0 : Math.Round(value, 4),
                    CurrencyCode = "EUR"
                };
                normalAmazonSaleAmountStatisticsItemDetail.LocalCurrencyStatisticalAmount = new Money()
                {
                    Amount = localValue == 0 ? 0 : Math.Round(localValue, 4),
                    CurrencyCode = "GBP"
                };
                normalSaleAmountStatisticsListItem.Detail.Add(normalAmazonSaleAmountStatisticsItemDetail);
            }
            foreach (var item in newdeclareMonthList)//这里是2021年以后
            {
                var month = declareMonthDic[item];
                var localValue = newsale_Depart_Country_Datas.Where(c => c.ACTIVITY_PERIOD.Equals(item))
                    .Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_EXCL));

                var normalAmazonSaleAmountStatisticsItemDetail = new SaleAmountStatisticsItemDetail();
                normalAmazonSaleAmountStatisticsItemDetail.StatisticalPeriod = month;
                normalAmazonSaleAmountStatisticsItemDetail.ExchangeRate = 0;
                normalAmazonSaleAmountStatisticsItemDetail.StatisticalAmount = new Money()
                {
                    Amount = 0,
                    CurrencyCode = "EUR"
                };
                normalAmazonSaleAmountStatisticsItemDetail.LocalCurrencyStatisticalAmount = new Money()
                {
                    Amount = localValue == 0 ? 0 : Math.Round(localValue, 4),
                    CurrencyCode = "GBP"
                };
                normalSaleAmountStatisticsListItem.Detail.Add(normalAmazonSaleAmountStatisticsItemDetail);
            }
            result.NormalStatisticsInfo.NormalStatistics.Add(normalSaleAmountStatisticsListItem);

            //第十二步：根据第二步筛选保留的REFUND 、SALE 这两个字段的数据，做第二次筛选。
            //在SALE_ARRIVAL_COUNTRY列，筛选保留单元格为 (英国GB 德国DE) 这个字段的数据。
            var second_sale_Depart_Country_Datas = salesDatas.Where(c => c.SALE_ARRIVAL_COUNTRY.Equals(_countryCode)).ToList();

            //第十三步：在SALE_DEPART_COUNTRY 列，筛选保留除了英国以外的所有欧盟国家的数据。（保留字段为：IT、DE、SE等等，除去英国GB 德国DE）
            var sale_Depart_Country_exceptGB_Datas = second_sale_Depart_Country_Datas.Where(c => !c.SALE_DEPART_COUNTRY.Equals(_countryCode)
            && euCountryList.Contains(c.SALE_DEPART_COUNTRY)).ToList();

            //第十四步：在TRANSACTION_CURRENCY_CODE 列，筛选保留单元格为 GBP 这个字段的数据。
            var transaction_currency_code_GBP_Datas = sale_Depart_Country_exceptGB_Datas.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals("GBP")).ToList();
            var transaction_currency_code_EUR_Datas = sale_Depart_Country_exceptGB_Datas.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals("EUR")).ToList();

            decimal totalexceptVatCountryCodeActivityValue = 0;
            var tempValue = transaction_currency_code_GBP_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

            //返回结果 欧盟发往英国Item赋值
            var computationalNameOfEUToGB = $@"欧盟发往英国的销售金额";
            var normalSaleAmountEUToGBStatisticsListItem = new SaleAmountStatisticsItem();
            normalSaleAmountEUToGBStatisticsListItem.ComputationalName = computationalNameOfEUToGB;
            normalSaleAmountEUToGBStatisticsListItem.VatDeclareCountryAmount = new Money
            {
                Amount = Math.Round(tempValue, 4),
                CurrencyCode = "GBP"
            };

            totalexceptVatCountryCodeActivityValue += tempValue;
            if (transaction_currency_code_EUR_Datas.Count() > 0)
            {
                foreach (var item in declareMonthList)
                {
                    var month = declareMonthDic[item];
                    var exchangeRate = exchangeRateList.Where(c => c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();
                    var transation_EUR_Datas = transaction_currency_code_EUR_Datas.Where(c => c.TRANSACTION_CURRENCY_CODE.Equals("EUR")
                    && c.ACTIVITY_PERIOD.Equals(item)).ToList();
                    var value = transation_EUR_Datas.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));
                    if (value != 0)
                    {
                        totalexceptVatCountryCodeActivityValue += value / exchangeRate;
                    }
                    var localValue = transaction_currency_code_GBP_Datas.Where(c => c.ACTIVITY_PERIOD.Equals(item))
                    .Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                    var normalAmazonSaleAmountStatisticsItemDetail = new SaleAmountStatisticsItemDetail();
                    normalAmazonSaleAmountStatisticsItemDetail.StatisticalPeriod = month;
                    normalAmazonSaleAmountStatisticsItemDetail.StatisticalAmount = new Money()
                    {
                        Amount = value == 0 ? 0 : Math.Round(value, 4),
                        CurrencyCode = "EUR"
                    };
                    normalAmazonSaleAmountStatisticsItemDetail.LocalCurrencyStatisticalAmount = new Money()
                    {
                        Amount = localValue == 0 ? 0 : Math.Round(localValue, 4),
                        CurrencyCode = "GBP"
                    };
                    normalAmazonSaleAmountStatisticsItemDetail.ExchangeRate = exchangeRate;
                    normalSaleAmountEUToGBStatisticsListItem.Detail.Add(normalAmazonSaleAmountStatisticsItemDetail);

                }
                foreach (var item in newdeclareMonthList)
                {
                    var month = declareMonthDic[item];

                    var normalAmazonSaleAmountStatisticsItemDetail = new SaleAmountStatisticsItemDetail();
                    normalAmazonSaleAmountStatisticsItemDetail.StatisticalPeriod = month;
                    normalAmazonSaleAmountStatisticsItemDetail.ExchangeRate = 0;
                    normalAmazonSaleAmountStatisticsItemDetail.StatisticalAmount = new Money()
                    {
                        Amount = 0,
                        CurrencyCode = "EUR"
                    };
                    normalAmazonSaleAmountStatisticsItemDetail.LocalCurrencyStatisticalAmount = new Money()
                    {
                        Amount = 0,
                        CurrencyCode = "GBP"
                    };
                    normalSaleAmountEUToGBStatisticsListItem.Detail.Add(normalAmazonSaleAmountStatisticsItemDetail);
                }
            }
            else
            {
                //无欧盟发往英国数据
                foreach (var item in declareMonthList)
                {
                    var month = declareMonthDic[item];
                    var exchangeRate = exchangeRateList.Where(c => c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();
                    var localValue = transaction_currency_code_GBP_Datas.Where(c => c.ACTIVITY_PERIOD.Equals(item))
                    .Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                    var normalAmazonSaleAmountStatisticsItemDetail = new SaleAmountStatisticsItemDetail();
                    normalAmazonSaleAmountStatisticsItemDetail.StatisticalPeriod = month;
                    normalAmazonSaleAmountStatisticsItemDetail.ExchangeRate = exchangeRate;
                    normalAmazonSaleAmountStatisticsItemDetail.StatisticalAmount = new Money()
                    {
                        Amount = 0,
                        CurrencyCode = "EUR"
                    };
                    normalAmazonSaleAmountStatisticsItemDetail.LocalCurrencyStatisticalAmount = new Money()
                    {
                        Amount = localValue == 0 ? 0 : Math.Round(localValue, 4),
                        CurrencyCode = "GBP"
                    };
                    normalSaleAmountEUToGBStatisticsListItem.Detail.Add(normalAmazonSaleAmountStatisticsItemDetail);
                }
                foreach (var item in newdeclareMonthList)
                {
                    var month = declareMonthDic[item];

                    var normalAmazonSaleAmountStatisticsItemDetail = new SaleAmountStatisticsItemDetail();
                    normalAmazonSaleAmountStatisticsItemDetail.StatisticalPeriod = month;
                    normalAmazonSaleAmountStatisticsItemDetail.ExchangeRate = 0;
                    normalAmazonSaleAmountStatisticsItemDetail.StatisticalAmount = new Money()
                    {
                        Amount = 0,
                        CurrencyCode = "EUR"
                    };
                    normalAmazonSaleAmountStatisticsItemDetail.LocalCurrencyStatisticalAmount = new Money()
                    {
                        Amount = 0,
                        CurrencyCode = "GBP"
                    };
                    normalSaleAmountEUToGBStatisticsListItem.Detail.Add(normalAmazonSaleAmountStatisticsItemDetail);
                }
            }

            var saleAmount = totalGBPActivityValue + totalEURActivityValue + totalexceptVatCountryCodeActivityValue;
            saleAmount = Math.Round(saleAmount, 2);
            result.NormalStatisticsInfo.NormalStatistics.Add(normalSaleAmountEUToGBStatisticsListItem);
            result.NormalStatisticsInfo.NormalSaleAmount = new Money()
            {
                Amount = saleAmount,
                CurrencyCode = "GBP"
            };
            result.NormalStatisticsInfo.NormalSaleAmountExclTax = new Money()
            {
                Amount = newCalcTotalAmount,
                CurrencyCode = "GBP"
            };
            result.NormalStatisticsInfo.NormalSaleAmountAmtTax = new Money()
            {
                Amount = newCalcTotalAmtAmount,
                CurrencyCode = "GBP"
            };
            result.OtherStatisticsInfo = null;
            result.AmazonSaleCalculateModel = null;
            return result;
        }
        #endregion

        public async Task<EBaySaleAmountStatistics> EBaySalesAmountCalculate(List<EBaySalesData> ebaySalesDatas, List<string> declareMonthList, List<VatExchangeRate> exchangeRateList)
        {
            //var nationalBusiness = await _nationalBusinessReadOnlyRepository.GetAsync(new MatchNationalBusinessByCountryCodeSpecification("GB"));
            var result = new EBaySaleAmountStatistics();

            ebaySalesDatas = ebaySalesDatas.Where(d => !d.SaleDate.IsNullOrBlank() && d.BuyerCountry == "United Kingdom" && d.ItemCountry == "GB").ToList();
            var ebayDeteilByMonthList = new List<EbaySaleAmountStatisticsItemDetailByMonth>();
            foreach (var declareMonth in declareMonthList)
            {
                var detailByMonth = GetEBayDataStatisticsDetail(ebaySalesDatas, declareMonth, exchangeRateList);
                ebayDeteilByMonthList.Add(detailByMonth);
            }
            result.EbaySaleAmountStatisticsItemDetailByMonths = ebayDeteilByMonthList;

            result.NormalSaleAmount = new Money(Math.Round(ebayDeteilByMonthList.Sum(p => p.Detail.LocalCurrencyStatisticalAmount.Amount + p.Detail.USDToGBPExchangeRate * p.Detail.USDStatisticalAmount.Amount), 2), _currencyCode);

            result.NormalSaleAmountAmtTax = new Money(Math.Round(ebayDeteilByMonthList.Sum(p => p.Detail.StatisticalTaxAmount.Amount + p.Detail.USDToGBPExchangeRate * p.Detail.USDStatisticalTaxAmount.Amount), 2), _currencyCode);

            return result;
        }
        private EbaySaleAmountStatisticsItemDetailByMonth GetEBayDataStatisticsDetail(List<EBaySalesData> ebaySalesDatas, string month, List<VatExchangeRate> exchangeRateList)
        {
            var detailByMonth = new EbaySaleAmountStatisticsItemDetailByMonth();
            //币种切换
            Dictionary<string, string> dicCurrency = new Dictionary<string, string>()
            {
                { "美元","US $"}
            };
            var monthNum = Convert.ToDateTime(month).ToString("yyyy-MM");
            bool beforeTaxReform = Convert.ToDateTime(month) < new DateTime(2021, 1, 1);
            var dicRate = exchangeRateList.Where(r => r.Month == monthNum).ToList().ToDictionary(c => dicCurrency[c.ExchangeCurrency], c => c.ExchangeRate); ;
            dicRate.Add("￡", 1);
            dicRate.Add("£", 1);
            dicRate.Add("GB ￡", 1);
            dicRate.Add("GB £", 1);
            var dicCurrencyCode = new Dictionary<string, string> { { "GB ￡", _currencyCode }, { "GB £", _currencyCode }, { "￡", _currencyCode }, { "£", _currencyCode }, { "US $", "USD" } };
            EbaySaleAmountStatisticsItemDetail detail = new EbaySaleAmountStatisticsItemDetail()
            {
                StatisticalPeriod = monthNum
            };
            var ebaySalesMonthDatas = ebaySalesDatas.Where(d => d.DispatchedMonth == month).ToList();
            detail.CurrencyItems = ebaySalesMonthDatas.GroupBy(d => d.CurrencyCode)
                .Select(d => new EbayCurrencyItem
                {
                    CurrencySalesAmount = new Money(Math.Round(d.Sum(ed => Convert.ToDecimal(ed.TotalPriceMoney)) / (1 + _taxrate), 2), dicCurrencyCode[d.Key]),
                    ExchangeRate = dicRate[d.Key],
                    CurrencyTaxAmount = new Money(Math.Round(d.Sum(ed => Convert.ToDecimal(ed.SellerCollectedTaxMoney.IsNullOrBlank() ? "0" : ed.SellerCollectedTaxMoney)), 2), dicCurrencyCode[d.Key]),
                    CurrencyCode = dicCurrencyCode[d.Key]
                }).ToList();
            //这里是没有除以汇率的
            detail.StatisticalTaxAmount = new Money(detail.CurrencyItems.Sum(d => Math.Round(d.CurrencyTaxAmount.Amount, 2)), _currencyCode);
            detail.LocalCurrencyStatisticalAmount = new Money(detail.CurrencyItems.Sum(d => Math.Round(d.CurrencySalesAmount.Amount, 2)), _currencyCode);
            detail.USDToGBPExchangeRate = exchangeRateList.FirstOrDefault(p => p.Month == monthNum).ExchangeRate;
            detail.GBPExchangeRate = 1;
            detail.USDStatisticalAmount = detail.CurrencyItems.FirstOrDefault(p => p.CurrencyCode == "US $") == null ? new Money(0, "USD") : new Money(detail.CurrencyItems.Where(p => p.CurrencyCode == "US $").Sum(d => Math.Round(d.CurrencySalesAmount.Amount, 2)), "USD");
            detail.USDStatisticalTaxAmount = detail.CurrencyItems.FirstOrDefault(p => p.CurrencyCode == "US $") == null ? new Money(0, "USD") : new Money(detail.CurrencyItems.Where(p => p.CurrencyCode == "US $").Sum(d => Math.Round(d.CurrencyTaxAmount.Amount, 2)), "USD");
            detailByMonth.DeclareMonth = month;
            detailByMonth.Detail = detail;
            return detailByMonth;
        }



        //public async Task<EBaySaleAmountStatistics> EBaySalesAmountCalculate2(List<EBaySalesData> ebaySalesDatas, List<string> declareMonthList, List<VatExchangeRate> exchangeRateList)
        //{
        //    var nationalBusiness = await _nationalBusinessReadOnlyRepository.GetAsync(new MatchNationalBusinessByCountryCodeSpecification("GB"));
        //    EBaySaleAmountStatistics result = new EBaySaleAmountStatistics();
        //    //区分21年前数据
        //    //统计每月币种金额
        //    //汇总总含税与不含税金额
        //    //DateTime pointTime = Convert.ToDateTime("2021-01-01");

        //    ebaySalesDatas = ebaySalesDatas.Where(d => !d.SaleDate.IsNullOrBlank() && d.BuyerCountry == "United Kingdom" && d.ItemCountry == "GB").ToList();
        //    //var exclTaxData= ebaySalesDatas.Where(d => Convert.ToDateTime(d.DispatchedDate) >= pointTime).ToList();
        //    // var inclTaxData = ebaySalesDatas.Where(d => Convert.ToDateTime(d.DispatchedDate) < pointTime).ToList();
        //    EbaySaleAmountStatisticsItem item = new EbaySaleAmountStatisticsItem();

        //    var details = GetEBayDataStatisticsDetail(ebaySalesDatas, declareMonthList, exchangeRateList);
        //    item.Detail = details;
        //    item.SaleAmountInclTax = new Money(details.Sum(d => d.LocalCurrencyStatisticalAmount.Amount), _currencyCode);
        //    item.SaleAmountExclTax = new Money(Math.Round(item.SaleAmountInclTax.Amount / decimal.Parse((1 + nationalBusiness.TaxRate / 100).ToString()), 2), _countryCode);
        //    item.StatisticalTaxAmount = new Money(details.Sum(d => d.StatisticalTaxAmount.Amount), _currencyCode);
        //    List<EbaySaleAmountStatisticsItem> normalStatistics = new List<EbaySaleAmountStatisticsItem>() { item };
        //    result.NormalStatistics = normalStatistics;
        //    result.TotalSaleAmountExclTax = new Money(normalStatistics.Sum(s => s.SaleAmountExclTax.Amount), _currencyCode);
        //    result.TotalSaleAmountInclTax = new Money(normalStatistics.Sum(s => s.SaleAmountInclTax.Amount), _currencyCode);
        //    result.TotalStatisticalTaxAmount = new Money(normalStatistics.Sum(s => s.StatisticalTaxAmount.Amount), _currencyCode);
        //    return result;
        //}




        //public List<EbaySaleAmountStatisticsItemDetail> GetEBayDataStatisticsDetail(List<EBaySalesData> ebaySalesDatas, List<string> declareMonthList, List<VatExchangeRate> exchangeRateList)
        //{
        //    List<EbaySaleAmountStatisticsItemDetail> items = new List<EbaySaleAmountStatisticsItemDetail>();
        //    //币种切换
        //    Dictionary<string, string> dicCurrency = new Dictionary<string, string>()
        //    {
        //        { "美元","US $"}
        //    };
        //    foreach (var month in declareMonthList)
        //    {
        //        var monthNum = Convert.ToDateTime(month).ToString("yyyy-MM");
        //        bool beforeTaxReform = Convert.ToDateTime(month) < new DateTime(2021, 1, 1);
        //        var dicRate = exchangeRateList.Where(r => r.Month == monthNum).ToList().ToDictionary(c => dicCurrency[c.ExchangeCurrency], c => c.ExchangeRate); ;
        //        dicRate.Add("￡", 1);
        //        dicRate.Add("£", 1);
        //        dicRate.Add("GB ￡", 1);
        //        //dicRate.Add("US $", 0.8M);
        //        EbaySaleAmountStatisticsItemDetail detail = new EbaySaleAmountStatisticsItemDetail()
        //        {
        //            StatisticalPeriod = month
        //        };
        //        var ebaySalesMonthDatas = ebaySalesDatas.Where(d => d.DispatchedMonth == month).ToList();
        //        detail.CurrencyItems = ebaySalesMonthDatas.GroupBy(d => d.CurrencyCode)
        //            .Select(d => new EbayCurrencyItem
        //            {
        //                CurrencySalesAmount = new Money(d.Sum(ed => Convert.ToDecimal(ed.TotalPriceMoney)), d.Key),
        //                ExchangeRate = dicRate[d.Key],
        //                CurrencyTaxAmount = new Money(d.Sum(ed => Convert.ToDecimal(ed.SellerCollectedTaxMoney)), d.Key)
        //            }).ToList();
        //        detail.StatisticalTaxAmount = new Money(detail.CurrencyItems.Sum(d => Math.Round(d.CurrencyTaxAmount.Amount / d.ExchangeRate, 2)), _currencyCode);
        //        detail.LocalCurrencyStatisticalAmount = new Money(detail.CurrencyItems.Sum(d => Math.Round(d.CurrencySalesAmount.Amount / d.ExchangeRate, 2)), _currencyCode);

        //        items.Add(detail);
        //    }
        //    return items;
        //}

        public void SetRateTime(DateTime start, DateTime end, DateTime effectiveTime)
        {

        }
        /// <summary>
        /// 申报税额计算
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Dictionary<string, Money> VatPayableCalculate(VatDeclaredCalcModel model)
        {
            decimal rate = model.TaxRate;
            // var amazonSaleAmount = Math.Round(model.AmazonSaleAmount, 2);
            // var amazonAmtTax = Math.Round(model.GBAmazonSaleTaxAmount, 2);
            var otherSaleTax = Math.Round(model.OtherSaleTax, 2);
            decimal otherdeclareAmount = Math.Round(model.OtherDeclaredSalesAmount, 2);
            //var declareSaleAmount = Math.Round(model.AmazonDeclaredSalesAmount, 2);
            //var otherDeclareSaleAmount = Math.Round(model.OtherDeclaredSalesAmount, 2);
            //var otherDeclareSaleAmountLater = Math.Round(model.OtherDeclaredSalesAmountLater, 2);
            //var declareSaleAmtAmount = Math.Round(model.AmazonDeclaredSalesAmountAmtTax, 2);
            var ebayTotalPriceAmount = Math.Round(model.EBayTotalPriceAmount, 2);
            //var ebaySoldForAmount = Math.Round(model.EBaySoldForAmount, 2);
            var ebaySellerCollectedTaxAmount = Math.Round(model.EBaySellerCollectedTaxAmount, 2);
            decimal importVatTaxAmount = 0;
            if (model.DeclareItems.ContainsKey("D0001"))
                importVatTaxAmount = Math.Round(model.DeclareItems["D0001"], 2);
            //var importVatTaxAmount = Math.Round(model.DeclareItems["D0001"], 2);//进口增值税
            decimal saleToEUAmount = 0;
            if (model.DeclareItems.ContainsKey("D0002"))
                saleToEUAmount = Math.Round(model.DeclareItems["D0002"], 2);
            //var saleToEUAmount = Math.Round(model.DeclareItems["D0002"], 2);//从英国销售到欧盟国家（不含英国）的免税总销售额
            decimal purchaseFromEUAmount = 0;
            if (model.DeclareItems.ContainsKey("D0003"))
                purchaseFromEUAmount = Math.Round(model.DeclareItems["D0003"], 2);
            //var purchaseFromEUAmount = Math.Round(model.DeclareItems["D0003"], 2);//从欧盟国家（不含英国）的不含税采购金额
            decimal gbPurchaseVat = 0;
            if (model.DeclareItems.ContainsKey("D0004"))
                gbPurchaseVat = Math.Round(model.DeclareItems["D0004"], 2);
            //var gbPurchaseVat = Math.Round(model.DeclareItems["D0004"], 2);//英国国内采购增值税
            decimal gbpva = 0;
            if (model.DeclareItems.ContainsKey("D0007"))//PVA递延
                gbpva = Math.Round(model.DeclareItems["D0007"], 2);

            var declareTotalAmount = Math.Round(model.GBAmazonSaleTaxAmount / 0.2M * 1.2M, 2);//含税销售总额
            //var exclDeclareTotalAmount = Math.Round(model.AmazonAmtTax + otherdeclareAmount + ebayTotalPriceAmount, 2);
            var salesVatAmount = Math.Round(model.GBAmazonSaleTaxAmount + ebaySellerCollectedTaxAmount + otherSaleTax + gbpva, 2);//第1项

            var purchaseFromEUVatAmount = Math.Round(purchaseFromEUAmount * rate, 2);//第2项
            var totalVatAmount = Math.Round(salesVatAmount + purchaseFromEUVatAmount, 2);//第3项
            var purcharsesVatAmount = Math.Round(importVatTaxAmount + purchaseFromEUVatAmount + gbPurchaseVat + gbpva, 2);//第4项
            var reclaimedVatAmount = totalVatAmount - purcharsesVatAmount;//第5项
            //var totalSalesAmount = Math.Round(amazonSaleAmount / 1.2M + ebayTotalPriceAmount / 1.2M + otherdeclareAmount/1.2M, 2);//第6项
            var totalSalesAmount = Math.Round(model.GBAmazonSaleAmount + model.GBMarketplaceTaxAmount + model.GBExportToEUAmount + model.GBExportToNonEUAmount, 2);//第6项
            var totalPurchasesAmount = Math.Round(purcharsesVatAmount / rate + purchaseFromEUAmount, 2);//第7项
            var totalSaleToEUAmount = Math.Round(model.GBExportToEUAmount, 2);//第8项
            var totalPurchaseFromEUAmount = Math.Round(purchaseFromEUAmount, 2);//第9项
            //var totalSalesAmountInclTax = Math.Round(declareTotalAmount + ebayTotalPriceAmount, 2);
            //var totalSalesAmountExclTax = Math.Round(model.AmazonDeclaredSalesAmountExclTax + otherDeclareSaleAmountLater + ebaySoldForAmount, 2);

            var data = new Dictionary<string, Money>() {
                { "SalesVatAmount",new Money( salesVatAmount,_currencyCode )},
                { "PurchaseFromEUVatAmount",new Money( purchaseFromEUVatAmount,_currencyCode )},
                { "TotalVatAmount",new Money( totalVatAmount,_currencyCode )},
                { "PurcharsesVatAmount",new Money( purcharsesVatAmount,_currencyCode )},
                { "ReclaimedVatAmount",new Money( reclaimedVatAmount,_currencyCode )},
                { "TotalSalesAmount",new Money( totalSalesAmount,_currencyCode )},
                { "TotalPurchasesAmount",new Money( totalPurchasesAmount,_currencyCode )},
                { "TotalSaleToEUAmount",new Money( totalSaleToEUAmount,_currencyCode )},
                { "TotalPurchaseFromEUAmount",new Money( totalPurchaseFromEUAmount,_currencyCode )},

                { "DeclaredTotalSalesAmount",new Money( declareTotalAmount,_currencyCode )},
                { "ImportVatTaxAmount",new Money( importVatTaxAmount,_currencyCode )},
                { "OtherDeduction",new Money( gbPurchaseVat,_currencyCode )},
                // 含税销售总额
                { "totalSalesAmountInclTax",new Money(declareTotalAmount,_currencyCode)},
                // 不含税销售总额6-8
                { "totalSalesAmountExclTax",new Money(totalSalesAmount-totalSaleToEUAmount,_currencyCode)}

            };
            return data;
        }
        /// <summary>
        /// 转换申报金额数据
        /// </summary>
        /// <param name="model"></param>
        /// <param name="deductionItems"></param>
        /// <returns></returns>
        public DeclaredDataModel ConvertToDeclaredData(VatDeclaredCalcModel model, List<DeductionItemModel> deductionItems)
        {
            var dic = VatPayableCalculate(model);
            var result = new DeclaredDataModel()
            {
                DeclaredConfirmItems = dic,
                DeductionItems = deductionItems,
                DeductionAmount = dic["OtherDeduction"],
                ImportVatTaxAmount = dic["ImportVatTaxAmount"],
                Interest = new Money(0, _currencyCode),
                SalesVatAmount = dic["SalesVatAmount"],
                TaxDue = dic["ReclaimedVatAmount"],
                TotalSalesAmount = dic["DeclaredTotalSalesAmount"],
                DeclareSaleAmountExclTax = dic["totalSalesAmountExclTax"],
                DeclareSaleAmountInclTax = dic["totalSalesAmountInclTax"]
            };
            return result;
        }

        public Task<AliexpressSaleAmountStatistics> AliexpressSalesAmountCalculate(List<AliexpressSalesData> customerSalesDatas, List<string> declareMonthList, List<string> euCountryList, List<VatExchangeRate> exchangeRateList, List<string> existVatCountryCodeList)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 2025年新规则亚马逊VAT税金计算
        /// </summary>
        /// <param name="customerSalesDatas"></param>
        /// <param name="declareMonthList"></param>
        /// <param name="euCountryList"></param>
        /// <param name="declareMonthDic"></param>
        /// <param name="arrivalCountryList"></param>
        /// <param name="exchangeRateList"></param>
        /// <param name="euCountryDic"></param>
        /// <param name="existVatCountryCodeList"></param>
        /// <returns></returns>
        public async Task<SaleAmountStatisticsResult> AmazonSalesAmountCalculate2025(List<CustomerSalesData> customerSalesDatas, List<string> declareMonthList, List<string> euCountryList,
           Dictionary<string, string> declareMonthDic, List<string> arrivalCountryList, List<VatExchangeRate> exchangeRateList, Dictionary<string, string> euCountryDic,
            List<string> existVatCountryCodeList)
        {
            var result = new SaleAmountStatisticsResult();
            var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
            _logger.LogError($"计算月份：{string.Join(',', declareMonthList)}");

            // 筛选申报期间的数据
            var salesDatas = customerSalesDatas.Where(c => declareMonthList.Contains(c.ACTIVITY_PERIOD) && c.TAX_COLLECTION_RESPONSIBILITY != null).ToList();

            decimal totalSellerAmount = 0; // 未被代扣代缴净销售额
            decimal totalSellerTaxAmount = 0; // 卖家需要自行缴纳的税金
            decimal totalMarketplaceTaxAmount = 0; // 被代扣代缴净销售额
            decimal totalExportToEUAmount = 0; // 出口到欧盟国家的销售额
            decimal totalExportToNonEUAmount = 0; // 出口到非欧盟国家的销售额

            foreach (var declareMonth in declareMonthList)
            {
                var month = declareMonthDic[declareMonth];
                var monthSalesData = salesDatas.Where(c => c.ACTIVITY_PERIOD == declareMonth).ToList();

                // 1. 未被代扣的订单（卖家需要自行缴纳税金）
                var sellerResponsibilityData = monthSalesData.Where(c =>
                    c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller").ToList();

                // 第一步：计算未代扣代缴订单的含税销售额
                var netAmount = CalculateSellerTaxableAmount(sellerResponsibilityData, exchangeRateList, month);
                totalSellerAmount += Math.Round(netAmount, 2);
                var step1Amount = Math.Round(netAmount * _taxrate, 2);
                // 第二步：计算"COMMINGLING_BUY"的税金
                var comminglingBuyTax = CalculateComminglingBuyTax(sellerResponsibilityData, exchangeRateList, month);

                // 第三步：计算卖家最终应缴纳税金
                var sellerFinalTax = step1Amount - comminglingBuyTax;
                totalSellerTaxAmount += sellerFinalTax;
                _logger.LogError($"当前计算月份：{month}；当前月份未代扣代缴需缴纳税金：{step1Amount}；当前月份COMMINGLING_BUY的税金：{comminglingBuyTax}");

                // 2. 被代扣代缴净销售额（平台已经代扣代缴，只申报不交税）
                var marketplaceTaxAmount = CalculateMarketplaceTaxAmount(monthSalesData, exchangeRateList, month);
                totalMarketplaceTaxAmount += marketplaceTaxAmount;

                // 3. 出口到欧盟国家的销售额
                var exportToEUData = monthSalesData.Where(c =>
                    c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller" &&
                    c.SALE_DEPART_COUNTRY == _countryCode &&
                    euCountryList.Contains(c.SALE_ARRIVAL_COUNTRY)).ToList();

                var exportToEUAmount = CalculateExportAmount(exportToEUData, exchangeRateList, month);
                totalExportToEUAmount += exportToEUAmount;

                // 4. 出口到非欧盟国家的销售额
                var exportToNonEUData = monthSalesData.Where(c =>
                    c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller" &&
                    c.SALE_DEPART_COUNTRY == _countryCode &&
                    !euCountryList.Contains(c.SALE_ARRIVAL_COUNTRY) &&
                    c.SALE_ARRIVAL_COUNTRY != _countryCode).ToList();

                var exportToNonEUAmount = CalculateExportAmount(exportToNonEUData, exchangeRateList, month);
                totalExportToNonEUAmount += exportToNonEUAmount;
            }

            var euStatistics = new GBAmazonSaleAmountStatistics2025()
            {
                // 未被代扣净销售额
                AmazonSaleAmount = new Money(totalSellerAmount, _currencyCode),
                AmazonSaleTaxAmount = new Money(totalSellerTaxAmount, _currencyCode),
                ExportToEUAmount = new Money(totalExportToEUAmount, _currencyCode),
                ExportToNonEUAmount = new Money(totalExportToNonEUAmount, _currencyCode),
                // 被代扣代缴净销售额
                MarketplaceTaxAmount = new Money(totalMarketplaceTaxAmount, _currencyCode),
            };
            result.GBAmazonSaleAmountStatistics2025 = euStatistics;
            _logger.LogError($"计算结果：未被代扣净销售额{totalSellerTaxAmount}，被代扣代缴净销售额{totalMarketplaceTaxAmount}");
            // 构建返回结果
            // result.GBAmazonSaleAmountStatistics = new GBAmazonSaleAmountStatistics
            // {
            //     NormalSaleAmount = new Money(totalSellerTaxAmount, _currencyCode),
            //     NormalSaleAmountAmtTax = new Money(Math.Round(totalSellerTaxAmount * _taxrate, 2), _currencyCode),
            //     MarketplaceSaleAmount = new Money(totalMarketplaceTaxAmount, _currencyCode),
            //     ExportToEUAmount = new Money(totalExportToEUAmount, _currencyCode),
            //     ExportToNonEUAmount = new Money(totalExportToNonEUAmount, _currencyCode)
            // };

            return result;
        }

        /// <summary>
        /// 计算未代扣代缴订单的含税销售额
        /// </summary>
        private decimal CalculateSellerTaxableAmount(List<CustomerSalesData> sellerData, List<VatExchangeRate> exchangeRateList, string month)
        {
            decimal totalAmount = 0;

            // 1. 去掉所有筛选，勾选CQ列为"SELLER"的数据
            var cqSellerData = sellerData.Where(c => c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller").ToList();

            // 2. F列勾选全部之后删除"COMMINGLING_BUY"的数据
            var filteredData = cqSellerData.Where(c => c.TRANSACTION_TYPE != "COMMINGLING_BUY").ToList();

            // 3. BP及BQ列均勾选为"GB"的数据
            var gbData = filteredData.Where(c =>
                c.SALE_DEPART_COUNTRY == _countryCode &&
                c.SALE_ARRIVAL_COUNTRY == _countryCode).ToList();

            // 4. CB列删除"JE, GY, GG, GX"的数据
            var validPostCodeData = gbData.Where(c =>
                c.VAT_CALCULATION_IMPUTATION_COUNTRY.IsNullOrBlank() ||
                (!c.VAT_CALCULATION_IMPUTATION_COUNTRY.StartsWith("JE") &&
                 !c.VAT_CALCULATION_IMPUTATION_COUNTRY.StartsWith("GY") &&
                 !c.VAT_CALCULATION_IMPUTATION_COUNTRY.StartsWith("GG") &&
                 !c.VAT_CALCULATION_IMPUTATION_COUNTRY.StartsWith("GX"))).ToList();
            _logger.LogError($"当前计算月份：{month}；当前月份未代扣代缴订单数据：{string.Join(',', validPostCodeData.Select(item => item.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL))}；");
            // 5. 求和BA列销售额，将此步求和后的数据做好登记
            // 需要先确认BB列是否有不同币种，如有需要按汇率换算成GBP之后再合并求和
            decimal b2cAmount = 0;
            var currencyGroups = validPostCodeData.Where(c =>
                c.BUYER_VAT_NUMBER.IsNullOrBlank() &&
                !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank())
                .GroupBy(c => c.TRANSACTION_CURRENCY_CODE);

            foreach (var group in currencyGroups)
            {
                var currencyCode = group.Key;
                var currencyAmount = group.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                // 获取汇率，转换为GBP
                decimal exchangeRate = 1M;
                if (currencyCode != _currencyCode) // _currencyCode应该是"GBP"
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode) && currencyDic.ContainsKey(_currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == currencyDic[_currencyCode] &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为GBP后累加
                b2cAmount += currencyAmount / exchangeRate;
            }
            _logger.LogError($"当前计算月份：{month}；当前月份未代扣代缴销售额：{b2cAmount}；");

            // 6. 未被代扣代缴净销售额=第5步登记好的销售额/(1+税率)
            var netAmount = b2cAmount / (1 + _taxrate);

            // 7. 未代扣代缴缴纳税金=第6步计算出来的未被代扣代缴净销售额*税率
            // totalAmount = netAmount * _taxrate;

            return netAmount;  // Math.Round(totalAmount, 2);
        }

        /// <summary>
        /// 计算"COMMINGLING_BUY"的税金
        /// </summary>
        private decimal CalculateComminglingBuyTax(List<CustomerSalesData> sellerData, List<VatExchangeRate> exchangeRateList, string month)
        {
            // 1. 去掉所有筛选，勾选CQ列为"SELLER"的数据
            var cqSellerData = sellerData.Where(c => c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "seller").ToList();

            // 2. F列只勾选"COMMINGLING_BUY"的数据
            var comminglingBuyData = cqSellerData.Where(c => c.TRANSACTION_TYPE == "COMMINGLING_BUY").ToList();

            // 3. BP及BQ列均勾选为"GB"的数据
            var gbData = comminglingBuyData.Where(c =>
                c.SALE_DEPART_COUNTRY == _countryCode &&
                c.SALE_ARRIVAL_COUNTRY == _countryCode).ToList();

            // 4. CA列删除空白订单
            var validOrderData = gbData.Where(c => !c.BUYER_VAT_NUMBER.IsNullOrBlank()).ToList();

            // 5. AQ列勾选不等于0的数据
            var nonZeroData = validOrderData.Where(c =>
                !c.TOTAL_ACTIVITY_VALUE_VAT_AMT.IsNullOrBlank() &&
                decimal.Parse(c.TOTAL_ACTIVITY_VALUE_VAT_AMT) != 0).ToList();
            _logger.LogError($"当前计算月份：{month}；当前月份未代扣代缴COMMINGLING_BUY订单数据：{string.Join(',', nonZeroData.Select(item => item.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL))}；");

            // 6. 求和AQ列税金，将此步求和后的数据做好登记
            // 求和前请先确认BB列是否有不同币种，如有需要按汇率换算成GBP之后再合并求和
            decimal totalTax = 0;
            var currencyGroups = nonZeroData.GroupBy(c => c.TRANSACTION_CURRENCY_CODE);

            foreach (var group in currencyGroups)
            {
                var currencyCode = group.Key;
                var currencyTaxAmount = group.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_VAT_AMT));

                // 获取汇率，转换为GBP
                decimal exchangeRate = 1M;
                if (currencyCode != _currencyCode) // _currencyCode应该是"GBP"
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode) && currencyDic.ContainsKey(_currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == currencyDic[_currencyCode] &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为GBP后累加
                totalTax += currencyTaxAmount / exchangeRate;
            }

            return Math.Round(totalTax, 2);
        }

        /// <summary>
        /// 计算被代扣代缴净销售额
        /// </summary>
        private decimal CalculateMarketplaceTaxAmount(List<CustomerSalesData> monthSalesData, List<VatExchangeRate> exchangeRateList, string month)
        {
            // 第 1 步：去掉所有筛选，筛选对应的申报时间，勾选 CQ 列为“MARKETPLACE”的数据；
            // 第 2 步：BQ 列勾选为“GB”的数据 ；
            // 第 3 步：BP 列全选；
            var marketplaceData = monthSalesData.Where(c =>
                    c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "marketplace" &&
                    // c.SALE_DEPART_COUNTRY == _countryCode &&
                    c.SALE_ARRIVAL_COUNTRY == _countryCode).ToList();
            // 第4步：求和BA列销售额
            // 求和前请先确认BB列是否有不同币种，如有需要按汇率换算成GBP之后再合并求和
            decimal step4Amount = 0;
            var step4CurrencyGroups = marketplaceData.Where(c =>
                c.BUYER_VAT_NUMBER.IsNullOrBlank() &&
                !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank())
                .GroupBy(c => c.TRANSACTION_CURRENCY_CODE);
            _logger.LogError($"当前计算月份：{month}；当前月份代扣代缴step4订单数据：{string.Join(',', marketplaceData.Select(item => item.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL))}；");

            foreach (var group in step4CurrencyGroups)
            {
                var currencyCode = group.Key;
                var currencyAmount = group.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                // 获取汇率，转换为GBP
                decimal exchangeRate = 1M;
                if (currencyCode != _currencyCode)
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode) && currencyDic.ContainsKey(_currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == currencyDic[_currencyCode] &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为GBP后累加
                step4Amount += currencyAmount / exchangeRate;
            }

            // 第5步：去掉所有筛选，筛选对应的申报时间，勾选CQ列为"MARKETPLACE"的数据，然后BP列勾选为"GB"的数据
            var step5Data = monthSalesData.Where(c =>
                c.TAX_COLLECTION_RESPONSIBILITY.ToLower() == "marketplace" &&
                c.SALE_DEPART_COUNTRY == _countryCode).ToList();

            // 第6步：BQ列删除"GB"
            var step6Data = step5Data.Where(c => c.SALE_ARRIVAL_COUNTRY != _countryCode).ToList();

            // 第7步：求和BA列销售额
            // 求和前请先确认BB列是否有不同币种，如有需要按汇率换算成GBP之后再合并求和
            decimal step7Amount = 0;
            var step7CurrencyGroups = step6Data.Where(c =>
                c.BUYER_VAT_NUMBER.IsNullOrBlank() &&
                !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank())
                .GroupBy(c => c.TRANSACTION_CURRENCY_CODE);
            _logger.LogError($"当前计算月份：{month}；当前月份代扣代缴step7订单数据：{string.Join(',', step6Data.Select(item => item.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL))}；");

            foreach (var group in step7CurrencyGroups)
            {
                var currencyCode = group.Key;
                var currencyAmount = group.Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                // 获取汇率，转换为GBP
                decimal exchangeRate = 1M;
                if (currencyCode != _currencyCode)
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode) && currencyDic.ContainsKey(_currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == currencyDic[_currencyCode] &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为GBP后累加
                step7Amount += currencyAmount / exchangeRate;
            }

            // 第8步：被代扣代缴净销售额=第4步+第7步
            var totalMarketplaceAmount = step4Amount + step7Amount;
            _logger.LogError($"当前计算月份：{month}；当前月份代扣代缴销售额1：{step4Amount}；当前月份代扣代缴销售额2：{step7Amount}；");
            return Math.Round(totalMarketplaceAmount, 2);
        }

        /// <summary>
        /// 计算出口销售额
        /// </summary>
        private decimal CalculateExportAmount(List<CustomerSalesData> exportData, List<VatExchangeRate> exchangeRateList, string month)
        {
            decimal totalAmount = 0;

            // 按币种分组计算
            var currencyGroups = exportData.GroupBy(c => c.TRANSACTION_CURRENCY_CODE);

            foreach (var group in currencyGroups)
            {
                var currencyCode = group.Key;
                var currencyData = group.ToList();

                // 求和BA列销售额，需要换成申报国币种
                var currencyAmount = currencyData.Where(c => !c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL.IsNullOrBlank())
                    .Sum(c => decimal.Parse(c.TOTAL_ACTIVITY_VALUE_AMT_VAT_INCL));

                // 获取汇率
                decimal exchangeRate = 1M;
                if (currencyCode != _currencyCode)
                {
                    var currencyDic = ConstantHelper.GetConstantInfoList(typeof(CurrencyCode)).ToDictionary(c => c.Value, c => c.Description);
                    if (currencyDic.ContainsKey(currencyCode) && currencyDic.ContainsKey(_currencyCode))
                    {
                        exchangeRate = exchangeRateList.Where(c =>
                            c.VatCountryCurrency == currencyDic[_currencyCode] &&
                            c.ExchangeCurrency == currencyDic[currencyCode] &&
                            c.Month.Equals(month)).Select(c => c.ExchangeRate).FirstOrDefault();

                        if (exchangeRate == 0) exchangeRate = 1M;
                    }
                }

                // 转换为申报国币种
                totalAmount += currencyAmount / exchangeRate;
            }

            return Math.Round(totalAmount, 2);
        }
    }
}
