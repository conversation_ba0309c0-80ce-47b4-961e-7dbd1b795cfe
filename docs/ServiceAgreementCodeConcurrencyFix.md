# 服务协议编码并发问题修复方案

## 问题描述

在 `AddServiceAgreementCommandHandler` 中，当多个用户同时创建服务协议时，可能出现重复的协议编码。原因是：

1. 原始实现通过查询当天协议数量来生成编码
2. 在高并发场景下，多个请求可能同时查询到相同的数量
3. 导致生成相同的协议编码，违反唯一性约束

## 解决方案

### 1. 创建序列管理实体

创建了 `ServiceAgreementCodeSequence` 实体来管理协议编码的序列号：

- **文件**: `src/Vat.Service.Domain/AggregatesModel/CrmAgg/ServiceAgreementCodeSequence.cs`
- **功能**: 为每个日期维护一个独立的序列计数器
- **字段**:
  - `DateKey`: 日期键（格式：yyyyMMdd）
  - `CurrentSequence`: 当前序号
  - `CreateTime`: 创建时间
  - `UpdateTime`: 更新时间

### 2. 创建仓储接口和实现

- **接口**: `src/Vat.Service.Domain/Repositories/Crm/IServiceAgreementCodeSequenceRepository.cs`
- **实现**: `src/Vat.Service.Repository.MongoDB/Crm/ServiceAgreementCodeSequenceRepository.cs`
- **规格**: `src/Vat.Service.Domain/AggregatesModel/CrmAgg/Specifications/MatchServiceAgreementCodeSequenceByDateKeySpecification.cs`

### 3. 修改编码生成逻辑

在 `AddServiceAgreementCommandHandler` 中实现了新的原子操作方法：

```csharp
private async Task<string> GenerateUniqueAgreementCodeAsync()
{
    // 在事务内操作序列记录
    _dbContext.BeginTransaction();
    
    // 查找或创建当天的序列记录
    var sequence = await _codeSequenceRepository.GetAsync(
        new MatchServiceAgreementCodeSequenceByDateKeySpecification(dateKey));
    
    if (sequence == null)
    {
        sequence = new ServiceAgreementCodeSequence(dateKey, 0);
        await _codeSequenceRepository.AddAsync(sequence);
    }
    
    // 原子性地获取下一个序号
    var nextSequence = sequence.GetNextSequence();
    var candidateCode = $"{datePrefix}{nextSequence.ToString().PadLeft(4, '0')}";
    
    // 更新序列记录
    await _codeSequenceRepository.UpdateAsync(sequence);
    
    await _dbContext.CommitAsync();
    return candidateCode;
}
```

### 4. 注册新的依赖

在 `src/Vat.Service.IoC.AutofacModules/RepositoryModule.cs` 中注册了新的仓储：

```csharp
builder.RegisterType<ServiceAgreementCodeSequenceRepository>()
    .As<IServiceAgreementCodeSequenceRepository>()
    .InstancePerLifetimeScope();
```

## 技术优势

### 1. 原子性保证
- 使用数据库事务确保序列号的原子性递增
- 避免了并发环境下的竞态条件

### 2. 性能优化
- 每个日期维护独立的序列，避免跨日期的锁竞争
- 减少了复杂的查询和计算逻辑

### 3. 可靠性增强
- 提供重试机制，处理临时的数据库连接问题
- 包含后备方案，使用时间戳确保编码唯一性

### 4. 可维护性
- 清晰的职责分离，序列管理独立于业务逻辑
- 易于扩展和测试

## 编码格式

生成的协议编码格式：`XY{yyyyMMdd}{序号}`

- 前缀：`XY`
- 日期：`yyyyMMdd` 格式
- 序号：4位数字，不足补零

示例：`XY202501100001`、`XY202501100002`

## 迁移注意事项

1. **数据库迁移**: 需要创建 `ServiceAgreementCodeSequences` 集合
2. **现有数据**: 现有的协议编码不受影响
3. **向后兼容**: 新的编码生成逻辑完全向后兼容

## 测试建议

1. **并发测试**: 模拟多个用户同时创建协议
2. **性能测试**: 验证新方案的性能表现
3. **故障恢复测试**: 测试数据库连接异常时的重试机制
4. **编码唯一性测试**: 确保生成的编码始终唯一

## 总结

通过引入序列管理机制，我们成功解决了服务协议编码的并发重复问题，提高了系统的可靠性和性能。新方案采用了数据库事务和原子操作，确保了编码生成的唯一性和一致性。
